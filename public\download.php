<?php
/**
 * Secure PDF Download Handler for Apex Billing System
 */

require_once __DIR__.'/../config/db.php';
require_once __DIR__.'/../config/security.php';

// Function to send error response
function sendError($code, $message) {
    http_response_code($code);
    echo json_encode(['error' => $message]);
    exit;
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    sendError(405, 'Method not allowed');
}

// Check rate limiting
if (!RateLimiter::checkLimit('form_submission')) {
    sendError(429, 'Too many requests. Please try again later.');
}

// Get invoice ID (preferred method) or filename (legacy)
$invoiceId = filter_var($_GET['id'] ?? 0, FILTER_VALIDATE_INT);
$filename = basename($_GET['file'] ?? '');

if ($invoiceId) {
    // Secure method: Get file path from database
    try {
        $invoice = $db->fetchOne(
            "SELECT pdf_path, invoice_number FROM invoices WHERE id = ?",
            [$invoiceId],
            'i'
        );

        if (!$invoice) {
            sendError(404, 'Invoice not found');
        }

        $path = $invoice['pdf_path'];
        $downloadFilename = $invoice['invoice_number'] . '.pdf';

    } catch (Exception $e) {
        error_log('Download error: ' . $e->getMessage());
        sendError(500, 'Database error');
    }

} elseif ($filename) {
    // Legacy method: Direct file access (less secure)
    if (!preg_match('/^[A-Za-z0-9_.-]+\.pdf$/', $filename)) {
        sendError(400, 'Invalid filename format');
    }

    $path = __DIR__ . '/invoices/' . $filename;
    $downloadFilename = $filename;

} else {
    sendError(400, 'No file specified');
}

// Validate file exists and is readable
if (!file_exists($path) || !is_readable($path)) {
    sendError(404, 'File not found or not accessible');
}

// Validate file is within allowed directory (prevent directory traversal)
$realPath = realpath($path);
$allowedDir = realpath(__DIR__ . '/invoices/');

if (!$realPath || !$allowedDir || strpos($realPath, $allowedDir) !== 0) {
    sendError(403, 'Access denied');
}

// Validate file type
$finfo = finfo_open(FILEINFO_MIME_TYPE);
$mimeType = finfo_file($finfo, $realPath);
finfo_close($finfo);

if ($mimeType !== 'application/pdf') {
    sendError(400, 'Invalid file type');
}

// Set security headers
header('X-Content-Type-Options: nosniff');
header('X-Frame-Options: DENY');
header('Cache-Control: private, no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Set download headers
header('Content-Type: application/pdf');
header('Content-Disposition: attachment; filename="' . addslashes($downloadFilename) . '"');
header('Content-Length: ' . filesize($realPath));

// Output file
readfile($realPath);
exit;
?>