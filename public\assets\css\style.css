/* Modern Professional Styling for Apex Billing System */
:root {
  --primary-color: #2563eb;
  --secondary-color: #64748b;
  --success-color: #059669;
  --danger-color: #dc2626;
  --warning-color: #d97706;
  --light-bg: #f8fafc;
  --card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --border-radius: 8px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 0.9rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

.container {
  max-width: 1200px;
}

/* Header Styling */
.page-header {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 2rem;
  margin-bottom: 2rem;
  border-left: 4px solid var(--primary-color);
}

.page-header h1 {
  color: var(--primary-color);
  font-weight: 700;
  margin-bottom: 0.5rem;
}

.page-header .subtitle {
  color: var(--secondary-color);
  font-size: 1.1rem;
}

/* Card Styling */
.invoice-card {
  background: white;
  border-radius: var(--border-radius);
  box-shadow: var(--card-shadow);
  padding: 2rem;
  margin-bottom: 2rem;
}

/* Form Styling */
.form-label {
  font-weight: 600;
  color: var(--secondary-color);
  margin-bottom: 0.5rem;
}

.form-control {
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  padding: 0.75rem;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.form-control:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
  outline: none;
}

.form-control:read-only {
  background-color: #f1f5f9;
  color: var(--secondary-color);
}

/* Table Styling */
.table-container {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--card-shadow);
  margin-bottom: 1.5rem;
}

#itemsTable {
  margin-bottom: 0;
}

.table thead th {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: white;
  font-weight: 600;
  border: none;
  padding: 1rem 0.75rem;
  font-size: 0.9rem;
}

.table tbody td {
  padding: 0.75rem;
  border-color: #e2e8f0;
  vertical-align: middle;
}

.table input {
  font-size: 0.9rem;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  padding: 0.5rem;
}

.table input:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
}

/* Button Styling */
.btn {
  border-radius: var(--border-radius);
  font-weight: 600;
  padding: 0.75rem 1.5rem;
  transition: all 0.2s ease;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, var(--primary-color), #3b82f6);
  color: white;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8, var(--primary-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-success {
  background: linear-gradient(135deg, var(--success-color), #10b981);
  color: white;
  font-size: 1.1rem;
  padding: 1rem 2rem;
}

.btn-success:hover {
  background: linear-gradient(135deg, #047857, var(--success-color));
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.btn-outline-primary {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.btn-outline-primary:hover {
  background: var(--primary-color);
  color: white;
  transform: translateY(-1px);
}

.btn-outline-danger {
  border: 2px solid var(--danger-color);
  color: var(--danger-color);
  background: transparent;
  padding: 0.5rem 0.75rem;
  font-size: 1.2rem;
  line-height: 1;
}

.btn-outline-danger:hover {
  background: var(--danger-color);
  color: white;
}

/* Summary Section */
.summary-card {
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  border: 2px solid #e2e8f0;
  border-radius: var(--border-radius);
  padding: 1.5rem;
}

.summary-card .form-label {
  color: var(--secondary-color);
  font-weight: 700;
}

.summary-card .form-control {
  font-weight: 600;
  font-size: 1rem;
}

.summary-card .form-control[readonly] {
  background: white;
  border-color: #cbd5e1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .page-header, .invoice-card {
    padding: 1.5rem;
  }

  .table-responsive {
    font-size: 0.8rem;
  }

  .btn {
    padding: 0.6rem 1.2rem;
  }
}

/* Animation */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.invoice-card {
  animation: fadeInUp 0.5s ease-out;
}

/* Loading State */
.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}