<?php
/**
 * Security Configuration for Apex Billing System
 */

// Start session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 1);
    ini_set('session.use_strict_mode', 1);
    session_start();
}

// CSRF Protection
class CSRFProtection {
    public static function generateToken() {
        if (!isset($_SESSION['csrf_token'])) {
            $_SESSION['csrf_token'] = bin2hex(random_bytes(32));
        }
        return $_SESSION['csrf_token'];
    }
    
    public static function validateToken($token) {
        return isset($_SESSION['csrf_token']) && hash_equals($_SESSION['csrf_token'], $token);
    }
    
    public static function getTokenField() {
        return '<input type="hidden" name="csrf_token" value="' . self::generateToken() . '">';
    }
}

// Input Sanitization
class InputSanitizer {
    public static function sanitizeString($input) {
        return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
    }
    
    public static function sanitizeEmail($email) {
        return filter_var(trim($email), FILTER_SANITIZE_EMAIL);
    }
    
    public static function sanitizeNumber($number, $min = null, $max = null) {
        $num = filter_var($number, FILTER_VALIDATE_FLOAT);
        if ($num === false) return false;
        if ($min !== null && $num < $min) return false;
        if ($max !== null && $num > $max) return false;
        return $num;
    }
    
    public static function sanitizeArray($array, $callback) {
        if (!is_array($array)) return [];
        return array_map($callback, $array);
    }
}

// Rate Limiting
class RateLimiter {
    private static $limits = [
        'invoice_creation' => ['max' => 10, 'window' => 3600], // 10 invoices per hour
        'form_submission' => ['max' => 20, 'window' => 3600]   // 20 form submissions per hour
    ];
    
    public static function checkLimit($action, $identifier = null) {
        if (!isset(self::$limits[$action])) return true;
        
        $identifier = $identifier ?: self::getClientIdentifier();
        $key = "rate_limit_{$action}_{$identifier}";
        
        if (!isset($_SESSION[$key])) {
            $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + self::$limits[$action]['window']];
        }
        
        $data = $_SESSION[$key];
        
        // Reset if window expired
        if (time() > $data['reset_time']) {
            $_SESSION[$key] = ['count' => 0, 'reset_time' => time() + self::$limits[$action]['window']];
            $data = $_SESSION[$key];
        }
        
        // Check if limit exceeded
        if ($data['count'] >= self::$limits[$action]['max']) {
            return false;
        }
        
        // Increment counter
        $_SESSION[$key]['count']++;
        return true;
    }
    
    private static function getClientIdentifier() {
        return hash('sha256', $_SERVER['REMOTE_ADDR'] . $_SERVER['HTTP_USER_AGENT']);
    }
}

// Security Headers
function setSecurityHeaders() {
    header('X-Content-Type-Options: nosniff');
    header('X-Frame-Options: DENY');
    header('X-XSS-Protection: 1; mode=block');
    header('Referrer-Policy: strict-origin-when-cross-origin');
    header('Content-Security-Policy: default-src \'self\'; script-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://kit.fontawesome.com; style-src \'self\' \'unsafe-inline\' https://cdn.jsdelivr.net https://fonts.googleapis.com; font-src \'self\' https://fonts.gstatic.com; img-src \'self\' data:;');
}

// Call security headers
setSecurityHeaders();
?>
