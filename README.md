# Apex Billing System - Enhanced Version

A modern, professional billing system for Apex Infotech with improved security, UI/UX, and functionality.

## 🚀 Key Improvements Made

### ✅ Fixed Critical GST Bug
- **Issue**: GST was showing 18% in PDF even when set to 0%
- **Solution**: Added proper `name` attribute to tax rate field and fixed backend processing
- **Result**: GST now correctly reflects user input (0% shows as 0%)

### 🎨 Modern UI/UX Design
- Professional gradient background and card-based layout
- Responsive design with Bootstrap 5.3.3
- Inter font family for modern typography
- Improved color scheme with CSS custom properties
- Enhanced form styling with better visual feedback
- Professional invoice template with company branding

### 🔒 Enhanced Security
- CSRF protection for all forms
- Rate limiting to prevent abuse
- Input sanitization and validation
- SQL injection protection with prepared statements
- Secure file download with path validation
- Security headers implementation
- Environment-based configuration

### 📊 Advanced Features
- **Dashboard**: Statistics overview with total invoices, revenue, clients
- **Search & Pagination**: Find invoices quickly with advanced search
- **Invoice Status**: Visual indicators for overdue, due soon, and active invoices
- **Better Error Handling**: Comprehensive error messages and logging
- **Improved PDF Generation**: Enhanced template with professional styling

### 🏗️ Code Structure Improvements
- Modular architecture with separate config files
- Database helper class for cleaner queries
- Object-oriented JavaScript with InvoiceManager class
- Comprehensive input validation
- Better error handling and logging
- Environment configuration support

## 📁 Project Structure

```
apex-bill/
├── config/
│   ├── db.php              # Database configuration with security
│   ├── security.php        # Security classes and functions
│   └── .env.example        # Environment configuration template
├── public/
│   ├── assets/
│   │   ├── css/style.css   # Modern professional styling
│   │   └── js/invoice.js   # Enhanced JavaScript functionality
│   ├── create_invoice.php  # Modern invoice creation form
│   ├── view_invoices.php   # Enhanced invoice management
│   ├── download.php        # Secure PDF download handler
│   └── index.php           # Professional dashboard
├── templates/
│   └── invoice_template.php # Professional PDF template
├── save_invoice.php        # Enhanced invoice processing
└── README.md              # This file
```

## 🛠️ Installation & Setup

1. **Database Setup**:
   ```sql
   # Import the existing database structure
   mysql -u root -p apex_billing < db/apex.sql
   ```

2. **Environment Configuration**:
   ```bash
   # Copy environment template
   cp config/.env.example config/.env
   
   # Edit .env with your database credentials
   nano config/.env
   ```

3. **Dependencies**:
   ```bash
   # Install Composer dependencies (if not already installed)
   composer install
   ```

4. **Permissions**:
   ```bash
   # Ensure invoices directory is writable
   chmod 755 public/invoices/
   ```

## 🎯 Key Features

### Invoice Creation
- ✅ Modern, intuitive interface
- ✅ Real-time calculation with visual feedback
- ✅ Dynamic item addition/removal
- ✅ Comprehensive form validation
- ✅ CSRF protection
- ✅ Professional PDF generation

### Invoice Management
- ✅ Search functionality (client, project, invoice number)
- ✅ Pagination for large datasets
- ✅ Status indicators (Active, Due Soon, Overdue)
- ✅ Secure PDF downloads
- ✅ Responsive table design

### Dashboard
- ✅ Quick statistics overview
- ✅ Recent invoices display
- ✅ Action cards for common tasks
- ✅ Professional design

### Security Features
- ✅ CSRF token validation
- ✅ Rate limiting
- ✅ Input sanitization
- ✅ SQL injection prevention
- ✅ Secure file handling
- ✅ Security headers

## 🔧 Configuration

### Environment Variables (.env)
```ini
# Application
APP_ENV=development
APP_DEBUG=true

# Database
DB_HOST=localhost
DB_NAME=apex_billing
DB_USER=root
DB_PASSWORD=

# Company Info
COMPANY_NAME="Apex Infotech"
COMPANY_EMAIL="<EMAIL>"
# ... (see .env.example for full list)
```

### Security Settings
- Session security with httponly and secure flags
- CSRF protection on all forms
- Rate limiting (10 invoices/hour, 20 submissions/hour)
- Input validation and sanitization
- Secure PDF downloads with database validation

## 🎨 UI/UX Improvements

### Design Elements
- **Color Scheme**: Professional blue gradient theme
- **Typography**: Inter font family for modern look
- **Layout**: Card-based design with proper spacing
- **Responsiveness**: Mobile-friendly responsive design
- **Animations**: Smooth transitions and hover effects

### User Experience
- **Form Validation**: Real-time validation with visual feedback
- **Loading States**: Button loading indicators
- **Notifications**: Toast notifications for user feedback
- **Error Handling**: Graceful error messages
- **Accessibility**: Proper ARIA labels and semantic HTML

## 📱 Browser Support

- ✅ Chrome 90+
- ✅ Firefox 88+
- ✅ Safari 14+
- ✅ Edge 90+

## 🔍 Testing

### Manual Testing Checklist
- [ ] Create invoice with 0% GST → PDF shows 0% GST ✅
- [ ] Create invoice with 18% GST → PDF shows 18% GST ✅
- [ ] Form validation works correctly ✅
- [ ] Search functionality works ✅
- [ ] Pagination works ✅
- [ ] PDF download works securely ✅
- [ ] Dashboard statistics display correctly ✅

## 🚀 Future Enhancements

### Planned Features
- [ ] Invoice editing functionality
- [ ] Email invoice sending
- [ ] Payment tracking
- [ ] Client management system
- [ ] Recurring invoices
- [ ] Invoice templates
- [ ] Export to Excel/CSV
- [ ] Multi-currency support

### Technical Improvements
- [ ] API endpoints for mobile app
- [ ] Database migrations system
- [ ] Automated testing suite
- [ ] Docker containerization
- [ ] Backup system

## 📞 Support

For technical support or questions:
- **Email**: <EMAIL>
- **Phone**: 98307 55095 / 75958 77104

## 📄 License

This project is proprietary software for Apex Infotech.

---

**Apex Infotech** - *Smart Digital Solutions to Elevate Your Business*
