CREATE DATABASE apex_billing CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE apex_billing;

-- Clients
CREATE TABLE clients (
  id            INT AUTO_INCREMENT PRIMARY KEY,
  name          VA<PERSON><PERSON><PERSON>(120) NOT NULL,
  email         VARCHAR(150),
  phone         VARCHAR(40),
  address       TEXT,
  created_at    TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Projects linked to clients
CREATE TABLE projects (
  id          INT AUTO_INCREMENT PRIMARY KEY,
  client_id   INT NOT NULL,
  name        VARCHAR(120) NOT NULL,
  description TEXT,
  start_date  DATE,
  end_date    DATE,
  FOREIGN KEY (client_id) REFERENCES clients(id) ON DELETE CASCADE
);

-- Invoice header table
CREATE TABLE invoices (
  id             INT AUTO_INCREMENT PRIMARY KEY,
  project_id     INT NOT NULL,
  invoice_number VARCHAR(40) UNIQUE,
  invoice_date   DATE,
  due_date       DATE,
  subtotal       DECIMAL(10,2),
  tax            DECIMAL(10,2),
  total          DECIMAL(10,2),
  pdf_path       VARCHAR(255),
  created_at     TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Line items
CREATE TABLE invoice_items (
  id          INT AUTO_INCREMENT PRIMARY KEY,
  invoice_id  INT NOT NULL,
  description VARCHAR(255) NOT NULL,
  quantity    INT DEFAULT 1,
  unit_price  DECIMAL(10,2) NOT NULL,
  total       DECIMAL(10,2) NOT NULL,
  FOREIGN KEY (invoice_id) REFERENCES invoices(id) ON DELETE CASCADE
);