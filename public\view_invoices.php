<?php
require_once __DIR__.'/../config/db.php';
require_once __DIR__.'/../config/security.php';

// Pagination settings
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// Search functionality
$search = InputSanitizer::sanitizeString($_GET['search'] ?? '');
$searchCondition = '';
$searchParams = [];
$searchTypes = '';

if (!empty($search)) {
    $searchCondition = "WHERE (clients.name LIKE ? OR projects.name LIKE ? OR invoices.invoice_number LIKE ?)";
    $searchTerm = "%{$search}%";
    $searchParams = [$searchTerm, $searchTerm, $searchTerm];
    $searchTypes = 'sss';
}

// Get total count for pagination
$countQuery = "SELECT COUNT(*) as total FROM invoices
               JOIN projects ON projects.id = invoices.project_id
               JOIN clients ON clients.id = projects.client_id
               {$searchCondition}";
$totalResult = $db->fetchOne($countQuery, $searchParams, $searchTypes);
$totalRecords = $totalResult['total'];
$totalPages = ceil($totalRecords / $limit);

// Get invoices with pagination
$query = "SELECT invoices.*, projects.name AS project, clients.name AS client
          FROM invoices
          JOIN projects ON projects.id = invoices.project_id
          JOIN clients ON clients.id = projects.client_id
          {$searchCondition}
          ORDER BY invoices.created_at DESC
          LIMIT ? OFFSET ?";

$params = array_merge($searchParams, [$limit, $offset]);
$types = $searchTypes . 'ii';
$invoices = $db->fetchAll($query, $params, $types);
?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Invoices – Apex Infotech</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div class="container py-5">
  <!-- Page Header -->
  <div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1>Invoice Management</h1>
        <p class="subtitle">View and manage all your invoices</p>
      </div>
      <div>
        <a href="../index.php" class="btn btn-outline-primary me-2">Dashboard</a>
        <a href="create_invoice.php" class="btn btn-primary">Create New Invoice</a>
      </div>
    </div>
  </div>

  <!-- Search and Filters -->
  <div class="invoice-card">
    <form method="GET" class="row g-3 align-items-end">
      <div class="col-md-8">
        <label class="form-label">Search Invoices</label>
        <input type="text" name="search" class="form-control" placeholder="Search by client, project, or invoice number..." value="<?= htmlspecialchars($search) ?>">
      </div>
      <div class="col-md-4">
        <button type="submit" class="btn btn-primary me-2">Search</button>
        <a href="view_invoices.php" class="btn btn-outline-secondary">Clear</a>
      </div>
    </form>
  </div>

  <!-- Results Summary -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <p class="text-muted mb-0">
      Showing <?= count($invoices) ?> of <?= number_format($totalRecords) ?> invoices
      <?= !empty($search) ? "(filtered by: \"" . htmlspecialchars($search) . "\")" : "" ?>
    </p>
    <div class="text-muted">
      Page <?= $page ?> of <?= $totalPages ?>
    </div>
  </div>

  <!-- Invoices Table -->
  <?php if (!empty($invoices)): ?>
  <div class="invoice-card">
    <div class="table-container">
      <table class="table table-hover mb-0">
        <thead>
          <tr>
            <th>Invoice #</th>
            <th>Client</th>
            <th>Project</th>
            <th>Date</th>
            <th>Due Date</th>
            <th class="text-end">Amount</th>
            <th>Status</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <?php foreach ($invoices as $invoice): ?>
          <tr>
            <td><strong><?= htmlspecialchars($invoice['invoice_number']) ?></strong></td>
            <td><?= htmlspecialchars($invoice['client']) ?></td>
            <td><?= htmlspecialchars($invoice['project']) ?></td>
            <td><?= date('d M Y', strtotime($invoice['invoice_date'])) ?></td>
            <td><?= date('d M Y', strtotime($invoice['due_date'])) ?></td>
            <td class="text-end">₹<?= number_format($invoice['total'], 2) ?></td>
            <td>
              <?php
              $dueDate = strtotime($invoice['due_date']);
              $today = time();
              if ($dueDate < $today): ?>
                <span class="badge bg-danger">Overdue</span>
              <?php elseif ($dueDate - $today < 7 * 24 * 3600): ?>
                <span class="badge bg-warning">Due Soon</span>
              <?php else: ?>
                <span class="badge bg-success">Active</span>
              <?php endif; ?>
            </td>
            <td>
              <?php if ($invoice['pdf_path'] && file_exists($invoice['pdf_path'])): ?>
                <a href="download.php?id=<?= $invoice['id'] ?>" class="btn btn-sm btn-outline-primary" title="Download PDF">
                  <i class="fas fa-download"></i>
                </a>
              <?php else: ?>
                <span class="text-muted" title="PDF not available">
                  <i class="fas fa-file-times"></i>
                </span>
              <?php endif; ?>
            </td>
          </tr>
          <?php endforeach; ?>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Pagination -->
  <?php if ($totalPages > 1): ?>
  <nav aria-label="Invoice pagination" class="mt-4">
    <ul class="pagination justify-content-center">
      <?php if ($page > 1): ?>
        <li class="page-item">
          <a class="page-link" href="?page=<?= $page - 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">Previous</a>
        </li>
      <?php endif; ?>

      <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
        <li class="page-item <?= $i === $page ? 'active' : '' ?>">
          <a class="page-link" href="?page=<?= $i ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>"><?= $i ?></a>
        </li>
      <?php endfor; ?>

      <?php if ($page < $totalPages): ?>
        <li class="page-item">
          <a class="page-link" href="?page=<?= $page + 1 ?><?= !empty($search) ? '&search=' . urlencode($search) : '' ?>">Next</a>
        </li>
      <?php endif; ?>
    </ul>
  </nav>
  <?php endif; ?>

  <?php else: ?>
  <div class="invoice-card text-center py-5">
    <h4 class="text-muted">No invoices found</h4>
    <p class="text-muted">
      <?= !empty($search) ? "No invoices match your search criteria." : "You haven't created any invoices yet." ?>
    </p>
    <a href="create_invoice.php" class="btn btn-primary">Create Your First Invoice</a>
  </div>
  <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>