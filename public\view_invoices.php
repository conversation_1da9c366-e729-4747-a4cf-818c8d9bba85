<?php
require_once __DIR__.'/../config/db.php';
$res = $conn->query("SELECT invoices.*, projects.name AS project, clients.name AS client FROM invoices JOIN projects ON projects.id=invoices.project_id JOIN clients ON clients.id=projects.client_id ORDER BY invoices.created_at DESC");
?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Invoices – Apex Infotech</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
<div class="container py-4">
  <h1 class="mb-4">Invoices</h1>
  <table class="table table-hover">
    <thead><tr><th>#</th><th>Client</th><th>Project</th><th>Date</th><th>Total</th><th></th></tr></thead>
    <tbody>
    <?php while($row=$res->fetch_assoc()): ?>
      <tr>
        <td><?=$row['invoice_number']?></td>
        <td><?=$row['client']?></td>
        <td><?=$row['project']?></td>
        <td><?=date('d M Y',strtotime($row['invoice_date']))?></td>
        <td>₹<?=number_format($row['total'],2)?></td>
        <td><a href="download.php?file=<?=urlencode(basename($row['pdf_path']))?>" class="btn btn-sm btn-primary">PDF</a></td>
      </tr>
    <?php endwhile; ?>
    </tbody>
  </table>
</div>
</body>
</html>