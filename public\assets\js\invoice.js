// Modern Invoice Management System
class InvoiceManager {
  constructor() {
    this.tbody = document.querySelector('#itemsTable tbody');
    this.addBtn = document.querySelector('#addRow');
    this.subEl = document.getElementById('subtotal');
    this.totalEl = document.getElementById('grandTotal');
    this.taxRate = document.getElementById('taxRate');
    this.submitBtn = document.getElementById('submitBtn');
    this.form = document.getElementById('invoiceForm');

    this.init();
  }

  init() {
    this.addBtn.addEventListener('click', () => this.addRow());
    this.taxRate.addEventListener('input', () => this.calcTotals());
    this.form.addEventListener('submit', (e) => this.handleSubmit(e));

    // Initialize with one row
    this.addRow();

    // Add input validation
    this.addValidation();
  }

  // Helper to format numbers to 2 decimal places
  fmt(n) {
    return Number.parseFloat(n || 0).toFixed(2);
  }

  // Calculate totals with improved precision
  calcTotals() {
    let subtotal = 0;

    this.tbody.querySelectorAll('tr').forEach(tr => {
      const qtyInput = tr.querySelector('.qty');
      const unitInput = tr.querySelector('.unit');
      const lineTotalInput = tr.querySelector('.lineTotal');

      const qty = parseFloat(qtyInput.value) || 0;
      const unit = parseFloat(unitInput.value) || 0;
      const line = qty * unit;

      lineTotalInput.value = this.fmt(line);
      subtotal += line;

      // Add visual feedback for calculations
      if (line > 0) {
        tr.classList.add('table-success');
        tr.classList.remove('table-warning');
      } else {
        tr.classList.add('table-warning');
        tr.classList.remove('table-success');
      }
    });

    this.subEl.value = this.fmt(subtotal);
    const taxRateValue = parseFloat(this.taxRate.value) || 0;
    const taxAmount = subtotal * (taxRateValue / 100);
    const total = subtotal + taxAmount;
    this.totalEl.value = this.fmt(total);

    // Update tax display
    this.updateTaxDisplay(taxAmount);
  }

  // Add new row with improved styling
  addRow(desc = '', qty = 1, unit = 0) {
    const tr = document.createElement('tr');
    tr.className = 'invoice-row';
    tr.innerHTML = `
      <td>
        <input name="desc[]" class="form-control" value="${desc}" placeholder="Enter description" required>
      </td>
      <td>
        <input name="qty[]" type="number" min="1" value="${qty}" class="form-control qty" required>
      </td>
      <td>
        <input name="unit[]" type="number" min="0" step="0.01" value="${unit}" class="form-control unit" placeholder="0.00" required>
      </td>
      <td>
        <input name="line[]" type="text" readonly class="form-control lineTotal" value="0.00">
      </td>
      <td class="text-center">
        <button type="button" class="btn btn-outline-danger btn-sm remove" title="Remove item">
          <i class="fas fa-trash"></i>
        </button>
      </td>
    `;

    this.tbody.appendChild(tr);

    // Add event listeners
    tr.querySelectorAll('.qty, .unit').forEach(inp => {
      inp.addEventListener('input', () => this.calcTotals());
      inp.addEventListener('blur', () => this.validateInput(inp));
    });

    tr.querySelector('.remove').addEventListener('click', () => {
      if (this.tbody.children.length > 1) {
        tr.remove();
        this.calcTotals();
      } else {
        this.showNotification('At least one item is required', 'warning');
      }
    });

    // Focus on description field for new rows
    if (desc === '') {
      tr.querySelector('input[name="desc[]"]').focus();
    }

    this.calcTotals();
  }

  // Input validation
  validateInput(input) {
    const value = parseFloat(input.value);
    if (input.classList.contains('qty') && value < 1) {
      input.value = 1;
      this.showNotification('Quantity must be at least 1', 'warning');
    }
    if (input.classList.contains('unit') && value < 0) {
      input.value = 0;
      this.showNotification('Unit price cannot be negative', 'warning');
    }
  }

  // Add form validation
  addValidation() {
    const clientName = document.querySelector('input[name="client_name"]');
    const projectName = document.querySelector('input[name="project_name"]');

    [clientName, projectName].forEach(input => {
      input.addEventListener('blur', () => {
        if (input.value.trim() === '') {
          input.classList.add('is-invalid');
        } else {
          input.classList.remove('is-invalid');
        }
      });
    });
  }

  // Handle form submission
  handleSubmit(e) {
    if (!this.validateForm()) {
      e.preventDefault();
      return false;
    }

    // Show loading state
    this.submitBtn.classList.add('loading');
    this.submitBtn.disabled = true;

    // The form will submit normally, but we show loading feedback
    setTimeout(() => {
      this.submitBtn.classList.remove('loading');
      this.submitBtn.disabled = false;
    }, 3000);
  }

  // Validate entire form
  validateForm() {
    const clientName = document.querySelector('input[name="client_name"]').value.trim();
    const projectName = document.querySelector('input[name="project_name"]').value.trim();

    if (!clientName || !projectName) {
      this.showNotification('Please fill in all required fields', 'error');
      return false;
    }

    // Check if at least one item has values
    const hasValidItems = Array.from(this.tbody.querySelectorAll('tr')).some(tr => {
      const desc = tr.querySelector('input[name="desc[]"]').value.trim();
      const qty = parseFloat(tr.querySelector('.qty').value) || 0;
      const unit = parseFloat(tr.querySelector('.unit').value) || 0;
      return desc && qty > 0 && unit >= 0;
    });

    if (!hasValidItems) {
      this.showNotification('Please add at least one valid item', 'error');
      return false;
    }

    return true;
  }

  // Update tax display
  updateTaxDisplay(taxAmount) {
    const taxRate = parseFloat(this.taxRate.value) || 0;
    const taxLabel = document.querySelector('label[for="taxRate"]');
    if (taxLabel) {
      taxLabel.textContent = `Tax Rate (${taxRate}%) - ₹${this.fmt(taxAmount)}`;
    }
  }

  // Show notifications
  showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentNode) {
        notification.remove();
      }
    }, 5000);
  }
}

// Initialize the invoice manager when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new InvoiceManager();
});