// Helper to format numbers to 2‑dp
const fmt = n => Number.parseFloat(n).toFixed(2);

const tbody   = document.querySelector('#itemsTable tbody');
const addBtn  = document.querySelector('#addRow');
const subEl   = document.getElementById('subtotal');
const totalEl = document.getElementById('grandTotal');
const taxRate = document.getElementById('taxRate');

function calcTotals() {
  let subtotal = 0;
  tbody.querySelectorAll('tr').forEach(tr => {
    const qty  = +tr.querySelector('.qty').value || 0;
    const unit = +tr.querySelector('.unit').value || 0;
    const line = qty * unit;
    tr.querySelector('.lineTotal').value = fmt(line);
    subtotal += line;
  });
  subEl.value = fmt(subtotal);
  const total = subtotal * (1 + (+taxRate.value || 0)/100);
  totalEl.value = fmt(total);
}

function addRow(desc='', qty=1, unit=0){
  const tr = document.createElement('tr');
  tr.innerHTML = `
    <td><input name="desc[]"      class="form-control" value="${desc}"></td>
    <td><input name="qty[]"  type="number" min="1" value="${qty}"  class="form-control qty"></td>
    <td><input name="unit[]" type="number" min="0" step="0.01" value="${unit}" class="form-control unit"></td>
    <td><input name="line[]" type="text" readonly class="form-control lineTotal" value="0.00"></td>
    <td><button type="button" class="btn btn-outline-danger btn-sm remove">×</button></td>`;
  tbody.appendChild(tr);
  tr.querySelectorAll('.qty, .unit').forEach(inp=>inp.addEventListener('input',calcTotals));
  tr.querySelector('.remove').addEventListener('click',()=>{tr.remove(); calcTotals();});
  calcTotals();
}

addBtn.addEventListener('click',()=>addRow());
// Initialize with one row
addRow();

taxRate.addEventListener('input', calcTotals);