<?php
/**
 * Apex Billing System - Invoice Processing
 * Enhanced version with improved error handling and validation
 */

require_once __DIR__.'/config/db.php';
require_once __DIR__.'/config/security.php';
require_once __DIR__.'/vendor/autoload.php';
use Dompdf\Dompdf;
use Dompdf\Options;

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 0);
ini_set('log_errors', 1);

// Function to sanitize input
function sanitizeInput($input) {
    return htmlspecialchars(trim($input), ENT_QUOTES, 'UTF-8');
}

// Function to validate numeric input
function validateNumeric($value, $min = 0, $max = null) {
    $num = filter_var($value, FILTER_VALIDATE_FLOAT);
    if ($num === false || $num < $min) return false;
    if ($max !== null && $num > $max) return false;
    return $num;
}

// Function to send JSON response
function sendJsonResponse($success, $message, $data = null) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ]);
    exit;
}

// Validate request method
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    sendJsonResponse(false, 'Invalid request method');
}

// Check rate limiting
if (!RateLimiter::checkLimit('invoice_creation')) {
    sendJsonResponse(false, 'Too many invoice creation attempts. Please try again later.');
}

// CSRF Protection
if (!isset($_POST['csrf_token']) || !CSRFProtection::validateToken($_POST['csrf_token'])) {
    sendJsonResponse(false, 'Invalid security token. Please refresh the page and try again.');
}

// Validate and sanitize input data using security class
$clientName = InputSanitizer::sanitizeString($_POST['client_name'] ?? '');
$projectName = InputSanitizer::sanitizeString($_POST['project_name'] ?? '');
$descs = InputSanitizer::sanitizeArray($_POST['desc'] ?? [], 'trim');
$qtys = $_POST['qty'] ?? [];
$units = $_POST['unit'] ?? [];
$subtotal = InputSanitizer::sanitizeNumber($_POST['subtotal'] ?? 0);
$total = InputSanitizer::sanitizeNumber($_POST['total'] ?? 0);
$taxRate = InputSanitizer::sanitizeNumber($_POST['taxRate'] ?? 0, 0, 100);

// Validation
$errors = [];

if (empty($clientName)) {
    $errors[] = 'Client name is required';
}

if (empty($projectName)) {
    $errors[] = 'Project name is required';
}

if (empty($descs) || !is_array($descs)) {
    $errors[] = 'At least one item description is required';
}

if ($subtotal === false) {
    $errors[] = 'Invalid subtotal amount';
}

if ($total === false) {
    $errors[] = 'Invalid total amount';
}

if ($taxRate === false) {
    $errors[] = 'Invalid tax rate';
}

// Validate line items
for ($i = 0; $i < count($descs); $i++) {
    if (empty(trim($descs[$i]))) {
        $errors[] = "Item " . ($i + 1) . " description is required";
    }

    $qty = validateNumeric($qtys[$i] ?? 0, 1);
    if ($qty === false) {
        $errors[] = "Item " . ($i + 1) . " quantity must be at least 1";
    }

    $unit = validateNumeric($units[$i] ?? 0, 0);
    if ($unit === false) {
        $errors[] = "Item " . ($i + 1) . " unit price must be non-negative";
    }
}

if (!empty($errors)) {
    sendJsonResponse(false, 'Validation failed: ' . implode(', ', $errors));
}

// Calculate tax amount
$taxAmount = $subtotal * ($taxRate / 100);

// Database transaction
$conn->begin_transaction();
try {
    // Insert or get client
    $stmt = $conn->prepare("INSERT INTO clients(name) VALUES (?) ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)");
    if (!$stmt) {
        throw new Exception('Failed to prepare client statement: ' . $conn->error);
    }

    $stmt->bind_param('s', $clientName);
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert/update client: ' . $stmt->error);
    }

    $clientId = $conn->insert_id;
    if (!$clientId) {
        $result = $conn->query("SELECT id FROM clients WHERE name='" . $conn->real_escape_string($clientName) . "' LIMIT 1");
        if (!$result || $result->num_rows === 0) {
            throw new Exception('Failed to get client ID');
        }
        $clientId = $result->fetch_assoc()['id'];
    }

    // Insert or get project
    $stmt = $conn->prepare("INSERT INTO projects(client_id,name) VALUES (?,?) ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)");
    if (!$stmt) {
        throw new Exception('Failed to prepare project statement: ' . $conn->error);
    }

    $stmt->bind_param('is', $clientId, $projectName);
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert/update project: ' . $stmt->error);
    }

    $projectId = $conn->insert_id;
    if (!$projectId) {
        $result = $conn->query("SELECT id FROM projects WHERE name='" . $conn->real_escape_string($projectName) . "' AND client_id=$clientId LIMIT 1");
        if (!$result || $result->num_rows === 0) {
            throw new Exception('Failed to get project ID');
        }
        $projectId = $result->fetch_assoc()['id'];
    }

    // Generate invoice number and dates
    $invoiceNo = 'INV-' . date('YmdHis') . '-' . str_pad($projectId, 4, '0', STR_PAD_LEFT);
    $today = date('Y-m-d');
    $due = date('Y-m-d', strtotime('+15 days'));

    // Insert invoice
    $stmt = $conn->prepare("INSERT INTO invoices(project_id,invoice_number,invoice_date,due_date,subtotal,tax,total) VALUES (?,?,?,?,?,?,?)");
    if (!$stmt) {
        throw new Exception('Failed to prepare invoice statement: ' . $conn->error);
    }

    $stmt->bind_param('isssddd', $projectId, $invoiceNo, $today, $due, $subtotal, $taxAmount, $total);
    if (!$stmt->execute()) {
        throw new Exception('Failed to insert invoice: ' . $stmt->error);
    }

    $invoiceId = $stmt->insert_id;
    if (!$invoiceId) {
        throw new Exception('Failed to get invoice ID');
    }

    // Insert line items
    $lineStmt = $conn->prepare("INSERT INTO invoice_items(invoice_id,description,quantity,unit_price,total) VALUES (?,?,?,?,?)");
    if (!$lineStmt) {
        throw new Exception('Failed to prepare line item statement: ' . $conn->error);
    }

    for ($i = 0; $i < count($descs); $i++) {
        $qty = (int)$qtys[$i];
        $unit = (float)$units[$i];
        $lineTotal = $qty * $unit;

        $lineStmt->bind_param('isidd', $invoiceId, $descs[$i], $qty, $unit, $lineTotal);
        if (!$lineStmt->execute()) {
            throw new Exception('Failed to insert line item ' . ($i + 1) . ': ' . $lineStmt->error);
        }
    }

    // Generate PDF
    ob_start();
    include __DIR__.'/templates/invoice_template.php';
    $html = ob_get_clean();

    if (empty($html)) {
        throw new Exception('Failed to generate invoice template');
    }

    // Configure Dompdf options
    $options = new Options();
    $options->set('defaultFont', 'DejaVu Sans');
    $options->set('isRemoteEnabled', false);
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isFontSubsettingEnabled', true);

    $dompdf = new Dompdf($options);
    $dompdf->loadHtml($html);
    $dompdf->setPaper('A4', 'portrait');
    $dompdf->render();
    $pdfOutput = $dompdf->output();

    if (empty($pdfOutput)) {
        throw new Exception('Failed to generate PDF');
    }

    // Save PDF file
    $pdfDir = __DIR__.'/public/invoices';
    if (!is_dir($pdfDir)) {
        if (!mkdir($pdfDir, 0755, true)) {
            throw new Exception('Failed to create invoices directory');
        }
    }

    $pdfFile = $pdfDir."/{$invoiceNo}.pdf";
    if (file_put_contents($pdfFile, $pdfOutput) === false) {
        throw new Exception('Failed to save PDF file');
    }

    // Update invoice with PDF path
    $stmt = $conn->prepare("UPDATE invoices SET pdf_path=? WHERE id=?");
    if (!$stmt) {
        throw new Exception('Failed to prepare PDF path update statement: ' . $conn->error);
    }

    $stmt->bind_param('si', $pdfFile, $invoiceId);
    if (!$stmt->execute()) {
        throw new Exception('Failed to update PDF path: ' . $stmt->error);
    }

    // Commit transaction
    $conn->commit();

    // Send PDF to browser
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="'.$invoiceNo.'.pdf"');
    header('Content-Length: ' . filesize($pdfFile));
    header('Cache-Control: private, max-age=0, must-revalidate');
    header('Pragma: public');

    readfile($pdfFile);
    exit;

} catch(Exception $e) {
    $conn->rollback();
    error_log('Invoice generation error: ' . $e->getMessage());

    // Check if this is an AJAX request
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        sendJsonResponse(false, 'Failed to generate invoice: ' . $e->getMessage());
    } else {
        // Redirect back with error message
        header('Location: public/create_invoice.php?error=' . urlencode($e->getMessage()));
        exit;
    }
}