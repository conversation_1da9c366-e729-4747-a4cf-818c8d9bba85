<?php
require_once __DIR__.'/config/db.php';
require_once __DIR__.'/vendor/autoload.php';
use Dompdf\Dompdf;

$clientName   = $_POST['client_name'];
$projectName  = $_POST['project_name'];
$descs        = $_POST['desc'];
$qtys         = $_POST['qty'];
$units        = $_POST['unit'];
$subtotal     = $_POST['subtotal'];
$total        = $_POST['total'];
$taxRate      = ($_POST['taxRate'] ?? 18);
$taxAmount    = $subtotal * ($taxRate / 100);

$conn->begin_transaction();
try {
  $stmt = $conn->prepare("INSERT INTO clients(name) VALUES (?) ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)");
  $stmt->bind_param('s',$clientName);
  $stmt->execute();
  $clientId = $conn->insert_id ?: $conn->query("SELECT id FROM clients WHERE name='$clientName' LIMIT 1")->fetch_assoc()['id'];

  $stmt = $conn->prepare("INSERT INTO projects(client_id,name) VALUES (?,?) ON DUPLICATE KEY UPDATE id=LAST_INSERT_ID(id)");
  $stmt->bind_param('is', $clientId, $projectName);
  $stmt->execute();
  $projectId = $conn->insert_id ?: $conn->query("SELECT id FROM projects WHERE name='$projectName' AND client_id=$clientId LIMIT 1")->fetch_assoc()['id'];

  $invoiceNo = 'INV-'.date('YmdHis');
  $today     = date('Y-m-d');
  $due       = date('Y-m-d', strtotime('+15 days'));
  $stmt = $conn->prepare("INSERT INTO invoices(project_id,invoice_number,invoice_date,due_date,subtotal,tax,total) VALUES (?,?,?,?,?,?,?)");
  $stmt->bind_param('isssddd',$projectId,$invoiceNo,$today,$due,$subtotal,$taxAmount,$total);
  $stmt->execute();
  $invoiceId = $stmt->insert_id;

  $lineStmt = $conn->prepare("INSERT INTO invoice_items(invoice_id,description,quantity,unit_price,total) VALUES (?,?,?,?,?)");
  for($i=0;$i<count($descs);$i++){
    $lineTotal = $qtys[$i]*$units[$i];
    $lineStmt->bind_param('isidd',$invoiceId,$descs[$i],$qtys[$i],$units[$i],$lineTotal);
    $lineStmt->execute();
  }

  ob_start();
  include __DIR__.'/templates/invoice_template.php';
  $html = ob_get_clean();
  $dompdf = new Dompdf();
  $dompdf->loadHtml($html);
  $dompdf->setPaper('A4','portrait');
  $dompdf->render();
  $pdfOutput = $dompdf->output();

  $pdfDir = __DIR__.'/public/invoices';
  if(!is_dir($pdfDir)) mkdir($pdfDir,0755,true);
  $pdfFile = $pdfDir."/{$invoiceNo}.pdf";
  file_put_contents($pdfFile, $pdfOutput);

  $stmt = $conn->prepare("UPDATE invoices SET pdf_path=? WHERE id=?");
  $stmt->bind_param('si',$pdfFile,$invoiceId);
  $stmt->execute();

  $conn->commit();

  header('Content-Type: application/pdf');
  header('Content-Disposition: attachment; filename="'.$invoiceNo.'.pdf"');
  readfile($pdfFile);
  exit;
} catch(Exception $e){
  $conn->rollback();
  die('Error: '.$e->getMessage());
}