<?php require_once __DIR__.'/config/db.php'; ?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Apex Infotech – Billing Dashboard</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="public/assets/css/style.css">
</head>
<body>
<div class="container py-5">
  <!-- Page Header -->
  <div class="page-header">
    <h1>Apex Billing Dashboard</h1>
    <p class="subtitle">Manage your invoices and billing efficiently</p>
  </div>

  <!-- Quick Stats -->
  <div class="row mb-4">
    <?php
    // Get quick statistics
    $totalInvoices = $conn->query("SELECT COUNT(*) as count FROM invoices")->fetch_assoc()['count'];
    $totalRevenue = $conn->query("SELECT SUM(total) as total FROM invoices")->fetch_assoc()['total'] ?? 0;
    $totalClients = $conn->query("SELECT COUNT(*) as count FROM clients")->fetch_assoc()['count'];
    $recentInvoices = $conn->query("SELECT COUNT(*) as count FROM invoices WHERE invoice_date >= DATE_SUB(NOW(), INTERVAL 30 DAY)")->fetch_assoc()['count'];
    ?>
    <div class="col-md-3 mb-3">
      <div class="invoice-card text-center">
        <h3 class="text-primary"><?= number_format($totalInvoices) ?></h3>
        <p class="mb-0">Total Invoices</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="invoice-card text-center">
        <h3 class="text-success">₹<?= number_format($totalRevenue, 2) ?></h3>
        <p class="mb-0">Total Revenue</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="invoice-card text-center">
        <h3 class="text-info"><?= number_format($totalClients) ?></h3>
        <p class="mb-0">Total Clients</p>
      </div>
    </div>
    <div class="col-md-3 mb-3">
      <div class="invoice-card text-center">
        <h3 class="text-warning"><?= number_format($recentInvoices) ?></h3>
        <p class="mb-0">This Month</p>
      </div>
    </div>
  </div>

  <!-- Action Cards -->
  <div class="row">
    <div class="col-md-6 mb-4">
      <div class="invoice-card h-100">
        <div class="d-flex align-items-center mb-3">
          <div class="bg-primary text-white rounded-circle p-3 me-3">
            <i class="fas fa-plus fa-lg"></i>
          </div>
          <div>
            <h4 class="mb-1">Create New Invoice</h4>
            <p class="text-muted mb-0">Generate professional invoices for your clients</p>
          </div>
        </div>
        <a href="public/create_invoice.php" class="btn btn-primary">Create Invoice</a>
      </div>
    </div>
    
    <div class="col-md-6 mb-4">
      <div class="invoice-card h-100">
        <div class="d-flex align-items-center mb-3">
          <div class="bg-success text-white rounded-circle p-3 me-3">
            <i class="fas fa-list fa-lg"></i>
          </div>
          <div>
            <h4 class="mb-1">View Invoices</h4>
            <p class="text-muted mb-0">Browse and manage all your invoices</p>
          </div>
        </div>
        <a href="public/view_invoices.php" class="btn btn-success">View All Invoices</a>
      </div>
    </div>
  </div>

  <!-- Recent Invoices -->
  <div class="invoice-card">
    <h4 class="mb-3">Recent Invoices</h4>
    <?php
    $recentQuery = "SELECT i.*, c.name as client_name, p.name as project_name 
                    FROM invoices i 
                    JOIN projects p ON i.project_id = p.id 
                    JOIN clients c ON p.client_id = c.id 
                    ORDER BY i.created_at DESC 
                    LIMIT 5";
    $recentResult = $conn->query($recentQuery);
    ?>
    
    <?php if ($recentResult && $recentResult->num_rows > 0): ?>
    <div class="table-container">
      <table class="table table-hover mb-0">
        <thead>
          <tr>
            <th>Invoice #</th>
            <th>Client</th>
            <th>Project</th>
            <th>Date</th>
            <th class="text-end">Amount</th>
            <th>Action</th>
          </tr>
        </thead>
        <tbody>
          <?php while ($invoice = $recentResult->fetch_assoc()): ?>
          <tr>
            <td><strong><?= htmlspecialchars($invoice['invoice_number']) ?></strong></td>
            <td><?= htmlspecialchars($invoice['client_name']) ?></td>
            <td><?= htmlspecialchars($invoice['project_name']) ?></td>
            <td><?= date('d M Y', strtotime($invoice['invoice_date'])) ?></td>
            <td class="text-end">₹<?= number_format($invoice['total'], 2) ?></td>
            <td>
              <?php if ($invoice['pdf_path'] && file_exists($invoice['pdf_path'])): ?>
              <a href="public/download.php?id=<?= $invoice['id'] ?>" class="btn btn-sm btn-outline-primary">Download</a>
              <?php else: ?>
              <span class="text-muted">PDF not available</span>
              <?php endif; ?>
            </td>
          </tr>
          <?php endwhile; ?>
        </tbody>
      </table>
    </div>
    <?php else: ?>
    <div class="text-center py-4">
      <p class="text-muted">No invoices found. <a href="public/create_invoice.php">Create your first invoice</a></p>
    </div>
    <?php endif; ?>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
</body>
</html>
