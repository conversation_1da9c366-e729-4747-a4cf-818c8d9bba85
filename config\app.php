<?php
/**
 * Application Configuration for Apex Billing System
 */

// Load environment variables
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    foreach ($env as $key => $value) {
        $_ENV[$key] = $value;
    }
}

// Application Settings
define('APP_NAME', $_ENV['COMPANY_NAME'] ?? 'Apex Infotech');
define('APP_ENV', $_ENV['APP_ENV'] ?? 'development');
define('APP_DEBUG', filter_var($_ENV['APP_DEBUG'] ?? false, FILTER_VALIDATE_BOOLEAN));

// Company Information
define('COMPANY_NAME', $_ENV['COMPANY_NAME'] ?? 'Apex Infotech');
define('COMPANY_ADDRESS', $_ENV['COMPANY_ADDRESS'] ?? '711204 – Lilua<PERSON>, Howrah, West Bengal');
define('COMPANY_PHONE', $_ENV['COMPANY_PHONE'] ?? '98307 55095 / 75958 77104');
define('COMPANY_EMAIL', $_ENV['COMPANY_EMAIL'] ?? '<EMAIL>');
define('COMPANY_PROPRIETORS', $_ENV['COMPANY_PROPRIETORS'] ?? 'Rajat Dey & Subhrashankar Dutta');
define('COMPANY_TAGLINE', $_ENV['COMPANY_TAGLINE'] ?? 'Smart Digital Solutions to Elevate Your Business');

// Invoice Settings
define('INVOICE_PREFIX', $_ENV['INVOICE_PREFIX'] ?? 'INV');
define('DEFAULT_TAX_RATE', (float)($_ENV['DEFAULT_TAX_RATE'] ?? 18));
define('DEFAULT_DUE_DAYS', (int)($_ENV['DEFAULT_DUE_DAYS'] ?? 15));
define('INVOICE_NUMBER_LENGTH', (int)($_ENV['INVOICE_NUMBER_LENGTH'] ?? 8));

// File Upload Settings
define('MAX_FILE_SIZE', (int)($_ENV['MAX_FILE_SIZE'] ?? 5242880)); // 5MB
define('ALLOWED_FILE_TYPES', explode(',', $_ENV['ALLOWED_FILE_TYPES'] ?? 'pdf,jpg,jpeg,png'));

// PDF Settings
define('PDF_FONT', $_ENV['PDF_FONT'] ?? 'DejaVu Sans');
define('PDF_PAPER_SIZE', $_ENV['PDF_PAPER_SIZE'] ?? 'A4');
define('PDF_ORIENTATION', $_ENV['PDF_ORIENTATION'] ?? 'portrait');

// Paths
define('INVOICE_STORAGE_PATH', __DIR__ . '/../public/invoices/');
define('TEMPLATE_PATH', __DIR__ . '/../templates/');

// Security Settings
define('SESSION_LIFETIME', (int)($_ENV['SESSION_LIFETIME'] ?? 3600));
define('CSRF_TOKEN_LIFETIME', (int)($_ENV['CSRF_TOKEN_LIFETIME'] ?? 3600));

// Email Settings (for future features)
define('MAIL_HOST', $_ENV['MAIL_HOST'] ?? 'smtp.gmail.com');
define('MAIL_PORT', (int)($_ENV['MAIL_PORT'] ?? 587));
define('MAIL_USERNAME', $_ENV['MAIL_USERNAME'] ?? '');
define('MAIL_PASSWORD', $_ENV['MAIL_PASSWORD'] ?? '');
define('MAIL_FROM_ADDRESS', $_ENV['MAIL_FROM_ADDRESS'] ?? COMPANY_EMAIL);
define('MAIL_FROM_NAME', $_ENV['MAIL_FROM_NAME'] ?? COMPANY_NAME);

// Helper Functions
class AppConfig {
    
    /**
     * Get company information as array
     */
    public static function getCompanyInfo() {
        return [
            'name' => COMPANY_NAME,
            'address' => COMPANY_ADDRESS,
            'phone' => COMPANY_PHONE,
            'email' => COMPANY_EMAIL,
            'proprietors' => COMPANY_PROPRIETORS,
            'tagline' => COMPANY_TAGLINE
        ];
    }
    
    /**
     * Get invoice settings as array
     */
    public static function getInvoiceSettings() {
        return [
            'prefix' => INVOICE_PREFIX,
            'default_tax_rate' => DEFAULT_TAX_RATE,
            'default_due_days' => DEFAULT_DUE_DAYS,
            'number_length' => INVOICE_NUMBER_LENGTH
        ];
    }
    
    /**
     * Get PDF settings as array
     */
    public static function getPdfSettings() {
        return [
            'font' => PDF_FONT,
            'paper_size' => PDF_PAPER_SIZE,
            'orientation' => PDF_ORIENTATION
        ];
    }
    
    /**
     * Generate next invoice number
     */
    public static function generateInvoiceNumber($lastNumber = null) {
        if ($lastNumber) {
            // Extract number from last invoice (e.g., INV00000123 -> 123)
            $number = (int)substr($lastNumber, strlen(INVOICE_PREFIX));
            $nextNumber = $number + 1;
        } else {
            $nextNumber = 1;
        }
        
        return INVOICE_PREFIX . str_pad($nextNumber, INVOICE_NUMBER_LENGTH, '0', STR_PAD_LEFT);
    }
    
    /**
     * Calculate due date from invoice date
     */
    public static function calculateDueDate($invoiceDate = null) {
        $date = $invoiceDate ? new DateTime($invoiceDate) : new DateTime();
        $date->add(new DateInterval('P' . DEFAULT_DUE_DAYS . 'D'));
        return $date->format('Y-m-d');
    }
    
    /**
     * Format currency amount
     */
    public static function formatCurrency($amount, $symbol = '₹') {
        return $symbol . number_format((float)$amount, 2);
    }
    
    /**
     * Validate file upload
     */
    public static function validateFileUpload($file) {
        $errors = [];
        
        // Check file size
        if ($file['size'] > MAX_FILE_SIZE) {
            $errors[] = 'File size exceeds maximum allowed size of ' . (MAX_FILE_SIZE / 1024 / 1024) . 'MB';
        }
        
        // Check file type
        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ALLOWED_FILE_TYPES)) {
            $errors[] = 'File type not allowed. Allowed types: ' . implode(', ', ALLOWED_FILE_TYPES);
        }
        
        return $errors;
    }
    
    /**
     * Get application version
     */
    public static function getVersion() {
        return '2.0.0'; // Enhanced version
    }
    
    /**
     * Check if debug mode is enabled
     */
    public static function isDebugMode() {
        return APP_DEBUG;
    }
    
    /**
     * Get environment
     */
    public static function getEnvironment() {
        return APP_ENV;
    }
}

// Initialize session with secure settings
if (session_status() === PHP_SESSION_NONE) {
    ini_set('session.cookie_lifetime', SESSION_LIFETIME);
    ini_set('session.gc_maxlifetime', SESSION_LIFETIME);
    session_start();
}

// Set error reporting based on environment
if (APP_ENV === 'production') {
    error_reporting(0);
    ini_set('display_errors', 0);
} else {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
}
?>
