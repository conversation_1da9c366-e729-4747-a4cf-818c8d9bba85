<?php
/**
 * Database Configuration for Apex Billing System
 * Enhanced with security features
 */

// Load environment variables if available
if (file_exists(__DIR__ . '/.env')) {
    $env = parse_ini_file(__DIR__ . '/.env');
    foreach ($env as $key => $value) {
        $_ENV[$key] = $value;
    }
}

// Database configuration
$host     = $_ENV['DB_HOST'] ?? "localhost";
$user     = $_ENV['DB_USER'] ?? "root";
$password = $_ENV['DB_PASSWORD'] ?? "";
$database = $_ENV['DB_NAME'] ?? "apex_billing";
$port     = $_ENV['DB_PORT'] ?? 3306;

// Create connection with error handling
try {
    $conn = new mysqli($host, $user, $password, $database, $port);

    // Set charset to prevent SQL injection
    $conn->set_charset("utf8mb4");

    // Check connection
    if ($conn->connect_error) {
        throw new Exception("Connection failed: " . $conn->connect_error);
    }

    // Set SQL mode for better data integrity
    $conn->query("SET sql_mode = 'STRICT_TRANS_TABLES,ERROR_FOR_DIVISION_BY_ZERO,NO_AUTO_CREATE_USER,NO_ENGINE_SUBSTITUTION'");

} catch (Exception $e) {
    error_log("Database connection error: " . $e->getMessage());

    // Don't expose database errors in production
    if ($_ENV['APP_ENV'] === 'production') {
        die("Database connection failed. Please try again later.");
    } else {
        die("Database connection failed: " . $e->getMessage());
    }
}

// Database helper functions
class DatabaseHelper {
    private $conn;

    public function __construct($connection) {
        $this->conn = $connection;
    }

    public function executeQuery($sql, $params = [], $types = '') {
        $stmt = $this->conn->prepare($sql);
        if (!$stmt) {
            throw new Exception("Prepare failed: " . $this->conn->error);
        }

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        if (!$stmt->execute()) {
            throw new Exception("Execute failed: " . $stmt->error);
        }

        return $stmt;
    }

    public function fetchOne($sql, $params = [], $types = '') {
        $stmt = $this->executeQuery($sql, $params, $types);
        $result = $stmt->get_result();
        return $result->fetch_assoc();
    }

    public function fetchAll($sql, $params = [], $types = '') {
        $stmt = $this->executeQuery($sql, $params, $types);
        $result = $stmt->get_result();
        return $result->fetch_all(MYSQLI_ASSOC);
    }
}

$db = new DatabaseHelper($conn);
?>