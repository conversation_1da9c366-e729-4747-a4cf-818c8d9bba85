<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Invoice <?= $invoiceNo ?></title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'DejaVu Sans', Arial, sans-serif;
      font-size: 12px;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 30px;
      background: #fff;
    }

    .invoice-container {
      max-width: 800px;
      margin: 0 auto;
      background: white;
      position: relative;
    }

    /* Header Section */
    .invoice-header {
      display: table;
      width: 100%;
      margin-bottom: 30px;
      border-bottom: 3px solid #2563eb;
      padding-bottom: 20px;
    }

    .company-section {
      display: table-cell;
      width: 60%;
      vertical-align: top;
    }

    .invoice-details {
      display: table-cell;
      width: 40%;
      vertical-align: top;
      text-align: right;
    }

    .company-name {
      font-size: 28px;
      font-weight: bold;
      color: #2563eb;
      margin-bottom: 8px;
      text-transform: uppercase;
      letter-spacing: 1px;
    }

    .company-tagline {
      font-size: 11px;
      color: #64748b;
      font-style: italic;
      margin-bottom: 15px;
    }

    .company-info {
      font-size: 11px;
      color: #475569;
      line-height: 1.5;
    }

    .invoice-title {
      font-size: 24px;
      font-weight: bold;
      color: #1e293b;
      margin-bottom: 10px;
    }

    .invoice-meta {
      font-size: 11px;
      color: #64748b;
    }

    .invoice-meta strong {
      color: #1e293b;
    }

    /* Client Section */
    .client-section {
      margin: 30px 0;
      padding: 20px;
      background: #f8fafc;
      border-left: 4px solid #2563eb;
    }

    .client-title {
      font-size: 14px;
      font-weight: bold;
      color: #1e293b;
      margin-bottom: 8px;
    }

    .client-info {
      font-size: 12px;
      color: #475569;
    }

    /* Items Table */
    .items-section {
      margin: 30px 0;
    }

    .table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .table thead th {
      background: linear-gradient(135deg, #2563eb, #3b82f6);
      color: white;
      font-weight: bold;
      padding: 12px 8px;
      text-align: left;
      font-size: 11px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }

    .table tbody td {
      padding: 10px 8px;
      border-bottom: 1px solid #e2e8f0;
      font-size: 11px;
    }

    .table tbody tr:nth-child(even) {
      background-color: #f8fafc;
    }

    .table tbody tr:hover {
      background-color: #f1f5f9;
    }

    .text-end {
      text-align: right;
    }

    .text-center {
      text-align: center;
    }

    /* Summary Section */
    .summary-section {
      margin-top: 30px;
      display: table;
      width: 100%;
    }

    .summary-spacer {
      display: table-cell;
      width: 60%;
    }

    .summary-content {
      display: table-cell;
      width: 40%;
      background: #f8fafc;
      padding: 20px;
      border: 1px solid #e2e8f0;
      border-radius: 8px;
    }

    .summary-row {
      display: table;
      width: 100%;
      margin-bottom: 8px;
    }

    .summary-label {
      display: table-cell;
      font-size: 11px;
      color: #64748b;
      padding-right: 20px;
    }

    .summary-value {
      display: table-cell;
      text-align: right;
      font-size: 11px;
      color: #1e293b;
      font-weight: 500;
    }

    .total-row {
      border-top: 2px solid #2563eb;
      padding-top: 10px;
      margin-top: 10px;
    }

    .total-row .summary-label {
      font-size: 14px;
      font-weight: bold;
      color: #1e293b;
    }

    .total-row .summary-value {
      font-size: 16px;
      font-weight: bold;
      color: #2563eb;
    }

    /* Footer */
    .invoice-footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e2e8f0;
      text-align: center;
    }

    .thank-you {
      font-size: 14px;
      color: #2563eb;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .footer-tagline {
      font-size: 10px;
      color: #64748b;
      font-style: italic;
    }

    /* Watermark */
    .watermark {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%) rotate(-45deg);
      font-size: 80px;
      color: rgba(37, 99, 235, 0.05);
      font-weight: bold;
      z-index: -1;
      pointer-events: none;
    }

    /* Currency Symbol */
    .currency {
      font-family: 'DejaVu Sans', Arial, sans-serif;
    }

    /* Print Styles */
    @media print {
      body {
        margin: 0;
        padding: 20px;
      }

      .invoice-container {
        box-shadow: none;
      }
    }
  </style>
</head>
<body>

  <div class="invoice-container">
    <!-- Watermark -->
    <div class="watermark">APEX</div>

    <!-- Header Section -->
    <div class="invoice-header">
      <div class="company-section">
        <div class="company-name">Apex Infotech</div>
        <div class="company-tagline">Smart Digital Solutions to Elevate Your Business</div>
        <div class="company-info">
          <strong>Address:</strong> 711204 – Liluah, Howrah, West Bengal<br>
          <strong>Contact:</strong> 98307 55095 / 75958 77104<br>
          <strong>Email:</strong> <EMAIL><br>
          <strong>Proprietors:</strong> Rajat Dey & Subhrashankar Dutta
        </div>
      </div>
      <div class="invoice-details">
        <div class="invoice-title">INVOICE</div>
        <div class="invoice-meta">
          <strong>Invoice #:</strong> <?= $invoiceNo ?><br>
          <strong>Date:</strong> <?= date('d M Y', strtotime($today)) ?><br>
          <strong>Due Date:</strong> <?= date('d M Y', strtotime($today . ' +15 days')) ?>
        </div>
      </div>
    </div>

    <!-- Client Information -->
    <div class="client-section">
      <div class="client-title">Bill To:</div>
      <div class="client-info">
        <strong><?= htmlspecialchars($clientName) ?></strong><br>
        Project: <?= htmlspecialchars($projectName) ?>
      </div>
    </div>

    <!-- Items Section -->
    <div class="items-section">
      <table class="table">
        <thead>
          <tr>
            <th style="width: 50%">Description</th>
            <th class="text-center" style="width: 15%">Qty</th>
            <th class="text-end" style="width: 20%">Unit Price</th>
            <th class="text-end" style="width: 15%">Total</th>
          </tr>
        </thead>
        <tbody>
          <?php for ($i = 0; $i < count($descs); $i++): ?>
          <tr>
            <td><?= htmlspecialchars($descs[$i]) ?></td>
            <td class="text-center"><?= number_format($qtys[$i]) ?></td>
            <td class="text-end"><span class="currency">₹</span><?= number_format($units[$i], 2) ?></td>
            <td class="text-end"><span class="currency">₹</span><?= number_format($qtys[$i] * $units[$i], 2) ?></td>
          </tr>
          <?php endfor; ?>
        </tbody>
      </table>
    </div>

    <!-- Summary Section -->
    <div class="summary-section">
      <div class="summary-spacer"></div>
      <div class="summary-content">
        <div class="summary-row">
          <div class="summary-label">Subtotal:</div>
          <div class="summary-value"><span class="currency">₹</span><?= number_format($subtotal, 2) ?></div>
        </div>
        <?php
        $taxAmount = $subtotal * ($taxRate / 100);
        if ($taxRate > 0):
        ?>
        <div class="summary-row">
          <div class="summary-label">Tax (<?= number_format($taxRate, 2) ?>%):</div>
          <div class="summary-value"><span class="currency">₹</span><?= number_format($taxAmount, 2) ?></div>
        </div>
        <?php endif; ?>
        <div class="summary-row total-row">
          <div class="summary-label">Grand Total:</div>
          <div class="summary-value"><span class="currency">₹</span><?= number_format($total, 2) ?></div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="invoice-footer">
      <div class="thank-you">Thank you for your business!</div>
      <div class="footer-tagline">Smart Digital Solutions to Elevate Your Business</div>
    </div>
  </div>
</body>
</html>
