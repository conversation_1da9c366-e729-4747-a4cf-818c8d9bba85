<?php require_once __DIR__.'/../config/db.php'; ?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Create Invoice – Apex Infotech</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body class="bg-light">
<div class="container py-5">
  <h1 class="mb-4">New Invoice</h1>
  <form id="invoiceForm" action="../save_invoice.php" method="post">
    <!-- Client / Project selectors (quick sample) -->
    <div class="row g-3 mb-3">
      <div class="col-md-6">
        <label class="form-label">Client Name</label>
        <input type="text" name="client_name" class="form-control" required>
      </div>
      <div class="col-md-6">
        <label class="form-label">Project</label>
        <input type="text" name="project_name" class="form-control" required>
      </div>
    </div>

    <!-- Dynamic invoice items -->
    <table class="table table-bordered" id="itemsTable">
      <thead class="table-light">
        <tr>
          <th>Description</th>
          <th style="width:120px">Qty</th>
          <th style="width:150px">Unit Price</th>
          <th style="width:150px">Total</th>
          <th style="width:60px"></th>
        </tr>
      </thead>
      <tbody>
        <!-- Row template injected by JS -->
      </tbody>
    </table>
    <button type="button" id="addRow" class="btn btn-sm btn-outline-primary mb-3">+ Add Item</button>

    <!-- Totals -->
    <div class="row justify-content-end">
      <div class="col-md-4">
        <div class="mb-2">
          <label class="form-label">Subtotal</label>
          <input type="text" readonly class="form-control" id="subtotal" name="subtotal" value="0.00">
        </div>
        <div class="mb-2">
          <label class="form-label">Tax (%)</label>
          <input type="number" class="form-control" id="taxRate" value="18">
        </div>
        <div class="mb-3">
          <label class="form-label">Total</label>
          <input type="text" readonly class="form-control" id="grandTotal" name="total" value="0.00">
        </div>
      </div>
    </div>

    <button type="submit" class="btn btn-success">Save & Download PDF</button>
  </form>
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="assets/js/invoice.js"></script>
</body>
</html>