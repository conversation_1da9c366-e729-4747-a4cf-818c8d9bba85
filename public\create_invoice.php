<?php
require_once __DIR__.'/../config/db.php';
require_once __DIR__.'/../config/security.php';
?>
<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <title>Create Invoice – Apex Infotech</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="assets/css/style.css">
</head>
<body>
<div class="container py-5">
  <!-- Page Header -->
  <div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
      <div>
        <h1>Create New Invoice</h1>
        <p class="subtitle">Generate professional invoices for your clients</p>
      </div>
      <div>
        <a href="../index.php" class="btn btn-outline-primary me-2">Dashboard</a>
        <a href="view_invoices.php" class="btn btn-outline-secondary">View Invoices</a>
      </div>
    </div>
  </div>

  <!-- Error/Success Messages -->
  <?php if (isset($_GET['error'])): ?>
  <div class="alert alert-danger alert-dismissible fade show" role="alert">
    <strong>Error:</strong> <?= htmlspecialchars($_GET['error']) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  <?php endif; ?>

  <?php if (isset($_GET['success'])): ?>
  <div class="alert alert-success alert-dismissible fade show" role="alert">
    <strong>Success:</strong> <?= htmlspecialchars($_GET['success']) ?>
    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
  </div>
  <?php endif; ?>

  <form id="invoiceForm" action="../save_invoice.php" method="post">
    <?= CSRFProtection::getTokenField() ?>
    <!-- Client Information Card -->
    <div class="invoice-card">
      <h4 class="mb-3">Client Information</h4>
      <div class="row g-3">
        <div class="col-md-6">
          <label class="form-label">Client Name *</label>
          <input type="text" name="client_name" class="form-control" placeholder="Enter client name" required>
        </div>
        <div class="col-md-6">
          <label class="form-label">Project Name *</label>
          <input type="text" name="project_name" class="form-control" placeholder="Enter project name" required>
        </div>
      </div>
    </div>

    <!-- Invoice Items Card -->
    <div class="invoice-card">
      <div class="d-flex justify-content-between align-items-center mb-3">
        <h4 class="mb-0">Invoice Items</h4>
        <button type="button" id="addRow" class="btn btn-outline-primary">
          <i class="fas fa-plus me-2"></i>Add Item
        </button>
      </div>

      <div class="table-container">
        <table class="table table-hover mb-0" id="itemsTable">
          <thead>
            <tr>
              <th>Description</th>
              <th style="width:120px">Qty</th>
              <th style="width:150px">Unit Price (₹)</th>
              <th style="width:150px">Total (₹)</th>
              <th style="width:60px">Action</th>
            </tr>
          </thead>
          <tbody>
            <!-- Row template injected by JS -->
          </tbody>
        </table>
      </div>
    </div>

    <!-- Invoice Summary Card -->
    <div class="row justify-content-end">
      <div class="col-md-5">
        <div class="summary-card">
          <h5 class="mb-3">Invoice Summary</h5>
          <div class="mb-3">
            <label class="form-label">Subtotal</label>
            <div class="input-group">
              <span class="input-group-text">₹</span>
              <input type="text" readonly class="form-control" id="subtotal" name="subtotal" value="0.00">
            </div>
          </div>
          <div class="mb-3">
            <label class="form-label">Tax Rate (%)</label>
            <input type="number" class="form-control" id="taxRate" name="taxRate" value="0" min="0" max="100" step="0.01" placeholder="Enter tax percentage">
          </div>
          <div class="mb-4">
            <label class="form-label">Grand Total</label>
            <div class="input-group">
              <span class="input-group-text">₹</span>
              <input type="text" readonly class="form-control fw-bold" id="grandTotal" name="total" value="0.00">
            </div>
          </div>
          <button type="submit" class="btn btn-success w-100" id="submitBtn">
            <i class="fas fa-download me-2"></i>Generate & Download Invoice
          </button>
        </div>
      </div>
    </div>
  </form>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://kit.fontawesome.com/your-fontawesome-kit.js" crossorigin="anonymous"></script>
<script src="assets/js/invoice.js"></script>
</body>
</html>